<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优秀伴学师 | 赶考状元</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Serif SC', serif;
            margin: 0;
            padding: 0;
            background: #000;
            color: white;
            overflow-x: hidden;
        }

        .hero-section {
            position: relative;
            height: 50vh;
            background: linear-gradient(135deg, #1a1a1a 0%, #000 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .hero-section::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(ellipse at center, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
            z-index: 1;
        }

        .brand-logo {
            position: absolute;
            top: 40px;
            right: 40px;
            font-size: 18px;
            font-weight: 500;
            color: #d4af37;
            letter-spacing: 3px;
            z-index: 3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            max-width: 1000px;
            padding: 0 20px;
        }

        .main-title {
            font-size: 48px;
            font-weight: 700;
            color: #d4af37;
            margin-bottom: 30px;
            margin-top: 40px;
            letter-spacing: 3px;
            text-shadow: 2px 2px 8px rgba(0,0,0,0.7);
            line-height: 1.2;
        }

        .subtitle {
            font-size: 16px;
            color: #ccc;
            margin-bottom: 80px;
            letter-spacing: 6px;
            font-weight: 300;
            position: relative;
            display: inline-block;
        }

        .subtitle::before,
        .subtitle::after {
            content: "";
            position: absolute;
            top: 50%;
            width: 50px;
            height: 1px;
            background: #d4af37;
            transform: translateY(-50%);
        }

        .subtitle::before {
            left: -70px;
        }

        .subtitle::after {
            right: -70px;
        }

        .teacher-showcase {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 60px;
            margin: 40px 0;
        }

        .teacher-image-container {
            position: relative;
            z-index: 2;
        }

        .teacher-image {
            width: 300px;
            height: 400px;
            object-fit: cover;
            border-radius: 15px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.6);
            border: 3px solid #d4af37;
            transition: all 0.3s ease;
        }

        .teacher-image:hover {
            transform: translateY(-10px);
            box-shadow: 0 35px 70px rgba(0,0,0,0.8);
        }

        .decorative-circle {
            position: absolute;
            width: 150px;
            height: 150px;
            border: 2px solid rgba(212, 175, 55, 0.3);
            border-radius: 50%;
            top: -40px;
            right: -40px;
            z-index: 1;
        }

        .decorative-circle::before {
            content: "";
            position: absolute;
            width: 100px;
            height: 100px;
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .golden-curve {
            position: absolute;
            bottom: -80px;
            left: 50%;
            transform: translateX(-50%);
            width: 400px;
            height: 120px;
            z-index: 1;
        }

        .teacher-info {
            position: relative;
            z-index: 2;
            color: white;
            text-align: left;
        }

        .teacher-info::before {
            content: "";
            position: absolute;
            top: -40px;
            left: -50px;
            right: -50px;
            bottom: -40px;
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.15) 0%, rgba(212, 175, 55, 0.05) 100%);
            border-radius: 60% 40% 70% 30%;
            z-index: -1;
            transform: rotate(-8deg);
            filter: blur(1px);
        }

        .teacher-name {
            font-size: 48px;
            font-weight: 700;
            color: #d4af37;
            margin-bottom: 20px;
            font-family: 'Noto Serif SC', serif;
            text-shadow: 2px 2px 8px rgba(0,0,0,0.5);
            line-height: 1.2;
        }

        .teacher-subject {
            font-size: 28px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 300;
            letter-spacing: 3px;
            position: relative;
            padding: 0 30px;
        }

        .teacher-subject::before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            width: 20px;
            height: 2px;
            background: linear-gradient(90deg, #d4af37, transparent);
            transform: translateY(-50%);
        }

        .teacher-subject::after {
            content: "";
            position: absolute;
            right: 0;
            top: 50%;
            width: 20px;
            height: 2px;
            background: linear-gradient(270deg, #d4af37, transparent);
            transform: translateY(-50%);
        }

        .golden-curve svg {
            width: 100%;
            height: 100%;
        }

        .content-section {
            background: white;
            color: #333;
            padding: 80px 0;
            position: relative;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section {
            background: #fff;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-left: 5px solid #d4af37;
        }

        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            color: #333;
        }

        .section-title h2 {
            font-size: 28px;
            font-weight: 600;
            color: #d4af37;
            margin-left: 15px;
        }

        .section-content {
            line-height: 1.8;
            color: #555;
            font-size: 16px;
        }

        .section-content p {
            margin-bottom: 15px;
        }

        .section-content ul {
            padding-left: 25px;
            margin: 25px 0;
        }

        .section-content li {
            margin-bottom: 10px;
        }

        .highlight {
            background: linear-gradient(to right, rgba(212, 175, 55, 0.2), transparent);
            padding: 2px 8px;
            border-radius: 4px;
            font-weight: 600;
            color: #d4af37;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-title {
                font-size: 42px;
                letter-spacing: 2px;
            }

            .teacher-showcase {
                flex-direction: column;
                gap: 30px;
                text-align: center;
            }

            .teacher-image {
                width: 250px;
                height: 320px;
            }

            .teacher-name {
                font-size: 36px;
            }

            .teacher-subject {
                font-size: 22px;
                letter-spacing: 2px;
            }

            .subtitle::before,
            .subtitle::after {
                width: 30px;
            }

            .subtitle::before {
                left: -50px;
            }

            .subtitle::after {
                right: -50px;
            }

            .brand-logo {
                top: 20px;
                right: 20px;
                font-size: 16px;
            }

            .section {
                padding: 30px 25px;
            }
        }

        @media (max-width: 480px) {
            .main-title {
                font-size: 36px;
            }

            .teacher-image {
                width: 220px;
                height: 280px;
            }

            .teacher-name {
                font-size: 32px;
            }

            .teacher-subject {
                font-size: 18px;
                letter-spacing: 1px;
            }

            .subtitle {
                letter-spacing: 4px;
            }
        }

    </style>
</head>
<body>
    <div class="hero-section">
        <div class="brand-logo">赶考状元</div>

        <div class="hero-content">
            <h1 class="main-title">高级启迪伴学师</h1>
            <div class="subtitle">专业教育，成就未来</div>

            <div class="teacher-showcase">
                <div class="teacher-image-container">
                    <img src="https://images.unsplash.com/photo-1544717305-2782549b5136?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80" alt="金膛教练" class="teacher-image">
                    <div class="decorative-circle"></div>
                </div>

                <div class="teacher-info">
                    <div class="teacher-name">金膛教练</div>
                    <div class="teacher-subject">伴学教龄：3年</div>
                </div>
            </div>

            <div class="golden-curve">
                <svg viewBox="0 0 400 120" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0,90 Q200,30 400,90" stroke="#d4af37" stroke-width="3" fill="none" opacity="0.8"/>
                </svg>
            </div>
        </div>
    </div>

    <div class="content-section">
        <div class="container">
            <!-- 教师简介 -->
            <div class="section">
                <div class="section-title">
                    <h2>教师简介</h2>
                </div>
                <div class="section-content">
                    <p><span class="highlight">经验：</span>3年线下冲刺班伴学经验，丰富先进带教理念，累计服务学员1000+</p>
                    <p><span class="highlight">平均提分：</span>熟系数学、物理、化学、生物学科学习方式引导</p>
                </div>
            </div>

            <!-- 教学风格 -->
            <div class="section">
                <div class="section-title">
                    <h2>教学风格</h2>
                </div>
                <div class="section-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px; margin-top: 20px;">
                        <div style="background: #f8f9ff; border-radius: 15px; padding: 30px; text-align: left; border-left: 4px solid #d4af37;">
                            <div style="font-size: 24px; color: #d4af37; margin-bottom: 15px;">�</div>
                            <div style="font-weight: 600; margin-bottom: 15px; color: #333; font-size: 18px;">认真负责</div>
                            <p style="color: #666; font-size: 15px; margin: 0; line-height: 1.6;">善于与家长沟通，各方面深入了解学员学习情况，精通规划学习计划</p>
                        </div>
                        <div style="background: #f8f9ff; border-radius: 15px; padding: 30px; text-align: left; border-left: 4px solid #d4af37;">
                            <div style="font-size: 24px; color: #d4af37; margin-bottom: 15px;">🧠</div>
                            <div style="font-weight: 600; margin-bottom: 15px; color: #333; font-size: 18px;">思路清晰</div>
                            <p style="color: #666; font-size: 15px; margin: 0; line-height: 1.6;">思维严谨，逻辑清晰，准确把控学科重难点易错点，善于归纳总结</p>
                        </div>
                    </div>

                    <div style="background: linear-gradient(135deg, #f8f9ff 0%, #e6ebff 100%); border-radius: 20px; padding: 35px; margin-top: 30px; text-align: center; border: 2px solid #d4af37; position: relative;">
                        <div style="font-size: 28px; color: #d4af37; margin-bottom: 20px;">💬</div>
                        <div style="font-weight: 700; margin-bottom: 20px; color: #d4af37; font-size: 20px;">老师寄语</div>
                        <p style="color: #333; font-size: 18px; margin: 0; line-height: 1.8; font-style: italic; font-weight: 500;">
                            "别做知识的搬运工，成为它的建筑师！我递砖块，你设计自己的思维大厦。"
                        </p>
                    </div>
                </div>
            </div>

            <!-- 学员评价 -->
            <div class="section">
                <div class="section-title">
                    <h2>学员评价</h2>
                </div>
                <div class="section-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; margin-top: 20px;">
                        <!-- 评价截图1 -->
                        <div style="background: #fff; border-radius: 15px; padding: 20px; box-shadow: 0 8px 25px rgba(0,0,0,0.1); border: 1px solid #e0e0e0; position: relative;">
                            <div style="background: #f5f5f5; border-radius: 10px; padding: 15px; margin-bottom: 15px;">
                                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                    <div style="width: 35px; height: 35px; background: #d4af37; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-right: 10px;">李</div>
                                    <div>
                                        <div style="font-weight: 600; color: #333; font-size: 14px;">李同学家长</div>
                                        <div style="font-size: 11px; color: #999;">今天 14:32</div>
                                    </div>
                                </div>
                                <div style="color: #333; font-size: 14px; line-height: 1.5;">
                                    金膛教练真的很负责！孩子数学从60分提升到85分，物理也有明显进步。教练会定期和我沟通孩子的学习情况，制定的学习计划很有针对性👍
                                </div>
                            </div>
                            <div style="color: #ffc107; font-size: 14px;">⭐⭐⭐⭐⭐</div>
                        </div>

                        <!-- 评价截图2 -->
                        <div style="background: #fff; border-radius: 15px; padding: 20px; box-shadow: 0 8px 25px rgba(0,0,0,0.1); border: 1px solid #e0e0e0; position: relative;">
                            <div style="background: #f5f5f5; border-radius: 10px; padding: 15px; margin-bottom: 15px;">
                                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                    <div style="width: 35px; height: 35px; background: #d4af37; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-right: 10px;">王</div>
                                    <div>
                                        <div style="font-weight: 600; color: #333; font-size: 14px;">王同学</div>
                                        <div style="font-size: 11px; color: #999;">昨天 20:15</div>
                                    </div>
                                </div>
                                <div style="color: #333; font-size: 14px; line-height: 1.5;">
                                    跟着金膛教练学了2个月，化学成绩从70分提升到90分！教练思路很清晰，能把复杂的知识点讲得很简单，而且总结的方法特别实用🔥
                                </div>
                            </div>
                            <div style="color: #ffc107; font-size: 14px;">⭐⭐⭐⭐⭐</div>
                        </div>

                        <!-- 评价截图3 -->
                        <div style="background: #fff; border-radius: 15px; padding: 20px; box-shadow: 0 8px 25px rgba(0,0,0,0.1); border: 1px solid #e0e0e0; position: relative;">
                            <div style="background: #f5f5f5; border-radius: 10px; padding: 15px; margin-bottom: 15px;">
                                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                                    <div style="width: 35px; height: 35px; background: #d4af37; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-right: 10px;">陈</div>
                                    <div>
                                        <div style="font-weight: 600; color: #333; font-size: 14px;">陈同学家长</div>
                                        <div style="font-size: 11px; color: #999;">3天前 16:48</div>
                                    </div>
                                </div>
                                <div style="color: #333; font-size: 14px; line-height: 1.5;">
                                    感谢金膛教练！孩子生物从不及格到现在稳定80+，教练不仅教学方法好，还很关心孩子的心理状态。正如教练说的，真的成为了思维的建筑师！💪
                                </div>
                            </div>
                            <div style="color: #ffc107; font-size: 14px;">⭐⭐⭐⭐⭐</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>