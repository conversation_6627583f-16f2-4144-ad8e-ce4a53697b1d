<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优秀伴学师 | 赶考状元</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Serif SC', serif;
            margin: 0;
            padding: 0;
            background: #000;
            color: white;
            overflow-x: hidden;
        }

        .hero-section {
            position: relative;
            height: 100vh;
            background: linear-gradient(135deg, #1a1a1a 0%, #000 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .hero-section::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(ellipse at center, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
            z-index: 1;
        }

        .brand-logo {
            position: absolute;
            top: 40px;
            right: 40px;
            font-size: 18px;
            font-weight: 500;
            color: #d4af37;
            letter-spacing: 3px;
            z-index: 3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            max-width: 1000px;
            padding: 0 20px;
        }

        .main-title {
            font-size: 56px;
            font-weight: 700;
            color: #d4af37;
            margin-bottom: 30px;
            letter-spacing: 4px;
            text-shadow: 2px 2px 8px rgba(0,0,0,0.7);
            line-height: 1.2;
        }

        .subtitle {
            font-size: 16px;
            color: #ccc;
            margin-bottom: 80px;
            letter-spacing: 6px;
            font-weight: 300;
            position: relative;
            display: inline-block;
        }

        .subtitle::before,
        .subtitle::after {
            content: "";
            position: absolute;
            top: 50%;
            width: 50px;
            height: 1px;
            background: #d4af37;
            transform: translateY(-50%);
        }

        .subtitle::before {
            left: -70px;
        }

        .subtitle::after {
            right: -70px;
        }

        .teacher-showcase {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .teacher-image-container {
            position: relative;
            z-index: 2;
        }

        .teacher-image {
            width: 300px;
            height: 400px;
            object-fit: cover;
            border-radius: 15px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.6);
            border: 3px solid #d4af37;
            transition: all 0.3s ease;
        }

        .teacher-image:hover {
            transform: translateY(-10px);
            box-shadow: 0 35px 70px rgba(0,0,0,0.8);
        }

        .decorative-circle {
            position: absolute;
            width: 150px;
            height: 150px;
            border: 2px solid rgba(212, 175, 55, 0.3);
            border-radius: 50%;
            top: -40px;
            right: -40px;
            z-index: 1;
        }

        .decorative-circle::before {
            content: "";
            position: absolute;
            width: 100px;
            height: 100px;
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .golden-curve {
            position: absolute;
            bottom: -80px;
            left: 50%;
            transform: translateX(-50%);
            width: 400px;
            height: 120px;
            z-index: 1;
        }

        .golden-curve svg {
            width: 100%;
            height: 100%;
        }

        .content-section {
            background: white;
            color: #333;
            padding: 80px 0;
            position: relative;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section {
            background: #fff;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-left: 5px solid #d4af37;
        }

        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            color: #333;
        }

        .section-title h2 {
            font-size: 28px;
            font-weight: 600;
            color: #d4af37;
            margin-left: 15px;
        }

        .section-content {
            line-height: 1.8;
            color: #555;
            font-size: 16px;
        }

        .section-content p {
            margin-bottom: 15px;
        }

        .section-content ul {
            padding-left: 25px;
            margin: 25px 0;
        }

        .section-content li {
            margin-bottom: 10px;
        }

        .highlight {
            background: linear-gradient(to right, rgba(212, 175, 55, 0.2), transparent);
            padding: 2px 8px;
            border-radius: 4px;
            font-weight: 600;
            color: #d4af37;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-title {
                font-size: 42px;
                letter-spacing: 2px;
            }

            .teacher-image {
                width: 250px;
                height: 320px;
            }

            .subtitle::before,
            .subtitle::after {
                width: 30px;
            }

            .subtitle::before {
                left: -50px;
            }

            .subtitle::after {
                right: -50px;
            }

            .brand-logo {
                top: 20px;
                right: 20px;
                font-size: 16px;
            }

            .section {
                padding: 30px 25px;
            }
        }

        @media (max-width: 480px) {
            .main-title {
                font-size: 36px;
            }

            .teacher-image {
                width: 220px;
                height: 280px;
            }

            .subtitle {
                letter-spacing: 4px;
            }
        }

    </style>
</head>
<body>
    <div class="hero-section">
        <div class="brand-logo">赶考状元</div>

        <div class="hero-content">
            <h1 class="main-title">优秀伴学师</h1>
            <div class="subtitle">专业教育，成就未来</div>

            <div class="teacher-showcase">
                <div class="teacher-image-container">
                    <img src="https://images.unsplash.com/photo-1544717305-2782549b5136?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80" alt="王雅文老师" class="teacher-image">
                    <div class="decorative-circle"></div>
                </div>
            </div>

            <div class="golden-curve">
                <svg viewBox="0 0 400 120" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0,90 Q200,30 400,90" stroke="#d4af37" stroke-width="3" fill="none" opacity="0.8"/>
                </svg>
            </div>
        </div>
    </div>

    <div class="content-section">
        <div class="container">
            <!-- 教师简介 -->
            <div class="section">
                <div class="section-title">
                    <h2>教师简介</h2>
                </div>
                <div class="section-content">
                    <p>王雅文老师拥有北京大学中文系硕士学位，15年一线教学经验，省级特级教师，语文学科带头人。曾担任高考语文阅卷组组长，深谙高考命题规律与评分标准。</p>
                    <p>王老师独创<span class="highlight">"三维一体"语文教学法</span>，将文学素养、应试技巧与思维训练完美结合。她善于引导学生感悟文字之美，培养深度阅读能力，同时针对高考考点进行精准突破。</p>
                    <p>教学成果：所带班级高考语文平均分132分，培养出5名高考作文满分学生，30余名学生考入北大、清华等顶尖学府中文系。</p>
                </div>
            </div>
        
        <!-- 伴学经验 -->
        <div class="section">
            <div class="section-title">
                <i class="fas fa-calendar-alt"></i>
                <h2>伴学经验</h2>
            </div>
            <div class="section-content">
                <p>王老师拥有丰富的<span class="highlight">个性化伴学经验</span>，深谙不同学生的学习特点：</p>
                <ul style="padding-left: 25px; margin: 25px 0;">
                    <li>1、<span class="highlight">基础薄弱型学生</span> - 从基础知识点入手，循序渐进建立知识体系</li>
                    <li>2、<span class="highlight">偏科严重型学生</span> - 针对性强化训练，快速补齐短板</li>
                    <li>3、<span class="highlight">成绩优秀型学生</span> - 深度拓展提升，冲刺高分段</li>
                    <li>4、<span class="highlight">考前冲刺型学生</span> - 精准押题训练，高效提分</li>
                </ul>
                <p>王老师善于根据学生特点制定<span class="highlight">个性化学习方案</span>，通过深度陪伴式教学，帮助每位学生找到最适合的学习方法，实现成绩突破。</p>
            </div>
        </div>
        
        <!-- 伴学风格 -->
        <div class="section">
            <div class="section-title">
                <i class="fas fa-chalkboard-teacher"></i>
                <h2>教学风格</h2>
            </div>
            <div class="teaching-styles">
                <div class="style-card">
                    <i class="fas fa-book-open"></i>
                    <div class="style-title">深度阅读</div>
                    <p>引导文本深度解析，培养文学鉴赏能力</p>
                </div>
                <div class="style-card">
                    <i class="fas fa-feather-alt"></i>
                    <div class="style-title">创意写作</div>
                    <p>激发创作灵感，提升写作技巧与文采</p>
                </div>
                <div class="style-card">
                    <i class="fas fa-brain"></i>
                    <div class="style-title">思维训练</div>
                    <p>培养批判性思维与逻辑表达能力</p>
                </div>
                <div class="style-card">
                    <i class="fas fa-heart"></i>
                    <div class="style-title">人文关怀</div>
                    <p>关注学生成长，激发内在学习动力</p>
                </div>
            </div>
        </div>
        
        <!-- 好评反馈 -->
        <div class="section">
            <div class="section-title">
                <i class="fas fa-star"></i>
                <h2>学员评价</h2>
            </div>
            
            <div class="reviews-container">
                <div class="review-item">
                    <div class="review-header">
                        <img src="https://randomuser.me/api/portraits/women/32.jpg" class="review-avatar">
                        <div class="review-info">
                            <div class="review-name">刘同学</div>
                            <div class="review-date">2023-07-18</div>
                        </div>
                    </div>
                    <div class="review-stars">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="review-content">
                        "王老师的课让我重新认识了语文！以前觉得古文枯燥，现在能体会到文字背后的历史和文化。高考语文132分，感谢王老师的悉心指导！"
                    </div>
                </div>
                
                <div class="review-item">
                    <div class="review-header">
                        <img src="https://randomuser.me/api/portraits/men/22.jpg" class="review-avatar">
                        <div class="review-info">
                            <div class="review-name">张同学</div>
                            <div class="review-date">2023-06-30</div>
                        </div>
                    </div>
                    <div class="review-stars">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="review-content">
                        "跟随王老师学习半年，我的作文从42分提升到58分！她教授的写作框架和素材运用方法非常实用，高考作文几乎押中题目，太神奇了！"
                    </div>
                </div>
            </div>
            
            <h3 style="margin: 35px 0 25px; color: var(--secondary); font-family: 'Noto Serif SC', serif; text-align: center; font-size: 24px;">
                <i class="fas fa-camera" style="margin-right: 10px;"></i>学员反馈截图
            </h3>
            <div class="screenshot-grid">
                <div class="screenshot">
                    <div>
                        <i class="fas fa-image"></i>
                        <div>反馈截图1</div>
                    </div>
                </div>
                <div class="screenshot">
                    <div>
                        <i class="fas fa-image"></i>
                        <div>反馈截图2</div>
                    </div>
                </div>
                <div class="screenshot">
                    <div>
                        <i class="fas fa-image"></i>
                        <div>反馈截图3</div>
                    </div>
                </div>
                <div class="screenshot">
                    <div>
                        <i class="fas fa-image"></i>
                        <div>反馈截图4</div>
                    </div>
                </div>
            </div>
        </div>

    </div>
    
    <script>
        // 添加滚动动画效果
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.section');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.animation = 'fadeIn 1s ease forwards';
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.1 });
            
            sections.forEach(section => {
                observer.observe(section);
            });
            

        });
    </script>
</body>
</html>