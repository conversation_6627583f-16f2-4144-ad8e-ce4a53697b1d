<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优秀伴学师 | 赶考状元</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Serif SC', serif;
            margin: 0;
            padding: 0;
            background: #000;
            color: white;
            overflow-x: hidden;
        }

        .hero-section {
            position: relative;
            min-height: 25vh;
            height: auto;
            background: linear-gradient(135deg, #1a1a1a 0%, #000 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: visible;
            padding: 20px 15px;
        }

        .hero-section::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(ellipse at center, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
            z-index: 1;
        }

        .brand-logo {
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 16px;
            font-weight: 500;
            color: #d4af37;
            letter-spacing: 2px;
            z-index: 10;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            max-width: 1000px;
            padding: 0 20px;
            margin-top: 10px;
        }

        .main-title {
            font-size: 36px;
            font-weight: 700;
            color: #d4af37;
            margin-bottom: 15px;
            margin-top: 10px;
            letter-spacing: 2px;
            text-shadow: 2px 2px 8px rgba(0,0,0,0.7);
            line-height: 1.1;
            position: relative;
        }

        .title-badge {
            display: inline-block;
            background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
            color: #000;
            padding: 8px 20px;
            border-radius: 25px 8px 25px 8px;
            font-size: 36px;
            font-weight: 700;
            letter-spacing: 2px;
            text-shadow: none;
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
            position: relative;
            margin-bottom: 15px;
        }

        .title-badge::before {
            content: "";
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), transparent);
            border-radius: 30px 12px 30px 12px;
            z-index: -1;
        }

        .title-decorations {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .geometric-shape {
            position: absolute;
            border: 1px solid rgba(212, 175, 55, 0.2);
        }

        .shape-1 {
            top: 10%;
            left: 10%;
            width: 40px;
            height: 40px;
            transform: rotate(45deg);
        }

        .shape-2 {
            top: 20%;
            right: 15%;
            width: 30px;
            height: 30px;
            border-radius: 50%;
        }

        .shape-3 {
            bottom: 30%;
            left: 20%;
            width: 0;
            height: 0;
            border-left: 20px solid transparent;
            border-right: 20px solid transparent;
            border-bottom: 35px solid rgba(212, 175, 55, 0.15);
            border: none;
        }

        .shape-4 {
            bottom: 20%;
            right: 10%;
            width: 50px;
            height: 25px;
            border-radius: 25px;
        }

        .subtitle {
            font-size: 14px;
            color: #ccc;
            margin-bottom: 20px;
            letter-spacing: 4px;
            font-weight: 300;
            position: relative;
            display: inline-block;
        }

        .subtitle::before,
        .subtitle::after {
            content: "";
            position: absolute;
            top: 50%;
            width: 30px;
            height: 1px;
            background: #d4af37;
            transform: translateY(-50%);
        }

        .subtitle::before {
            left: -45px;
        }

        .subtitle::after {
            right: -45px;
        }

        .teacher-showcase {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 30px;
            margin: 15px 0;
        }

        .teacher-image-container {
            position: relative;
            z-index: 2;
        }

        .teacher-image {
            width: 200px;
            height: 260px;
            object-fit: cover;
            border-radius: 12px;
            box-shadow: 0 15px 30px rgba(0,0,0,0.6);
            border: 2px solid #d4af37;
            transition: all 0.3s ease;
        }

        .teacher-image:hover {
            transform: translateY(-10px);
            box-shadow: 0 35px 70px rgba(0,0,0,0.8);
        }

        .decorative-circle {
            position: absolute;
            width: 150px;
            height: 150px;
            border: 2px solid rgba(212, 175, 55, 0.3);
            border-radius: 50%;
            top: -40px;
            right: -40px;
            z-index: 1;
        }

        .decorative-circle::before {
            content: "";
            position: absolute;
            width: 100px;
            height: 100px;
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .golden-curve {
            position: absolute;
            bottom: -80px;
            left: 50%;
            transform: translateX(-50%);
            width: 400px;
            height: 120px;
            z-index: 1;
        }

        .teacher-info {
            position: relative;
            z-index: 2;
            color: white;
            text-align: left;
        }

        .teacher-info::before {
            content: "";
            position: absolute;
            top: -40px;
            left: -50px;
            right: -50px;
            bottom: -40px;
            background: linear-gradient(135deg, rgba(212, 175, 55, 0.15) 0%, rgba(212, 175, 55, 0.05) 100%);
            border-radius: 60% 40% 70% 30%;
            z-index: -1;
            transform: rotate(-8deg);
            filter: blur(1px);
        }

        .teacher-name {
            font-size: 32px;
            font-weight: 700;
            color: #d4af37;
            margin-bottom: 12px;
            font-family: 'Noto Serif SC', serif;
            text-shadow: 2px 2px 8px rgba(0,0,0,0.5);
            line-height: 1.2;
        }

        .teacher-subject {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 300;
            letter-spacing: 2px;
            position: relative;
            padding: 0 20px;
        }

        .teacher-subject::before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            width: 20px;
            height: 2px;
            background: linear-gradient(90deg, #d4af37, transparent);
            transform: translateY(-50%);
        }

        .teacher-subject::after {
            content: "";
            position: absolute;
            right: 0;
            top: 50%;
            width: 20px;
            height: 2px;
            background: linear-gradient(270deg, #d4af37, transparent);
            transform: translateY(-50%);
        }

        .golden-curve svg {
            width: 100%;
            height: 100%;
        }

        .content-section {
            background: white;
            color: #333;
            padding: 80px 0;
            position: relative;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section {
            padding: 25px 20px;
            margin-bottom: 15px;
        }

        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            color: #333;
        }

        .section-title h2 {
            font-size: 24px;
            font-weight: 600;
            color: #d4af37;
            margin: 0;
            position: relative;
        }

        .section-title h2::before {
            content: "";
            position: absolute;
            left: -25px;
            top: 50%;
            width: 15px;
            height: 2px;
            background: #d4af37;
            transform: translateY(-50%);
        }

        .section-content {
            line-height: 1.6;
            color: #333;
            font-size: 15px;
        }

        .section-content p {
            margin-bottom: 12px;
        }

        .section-content ul {
            padding-left: 20px;
            margin: 15px 0;
        }

        .section-content li {
            margin-bottom: 8px;
        }

        .highlight {
            background: linear-gradient(to right, rgba(212, 175, 55, 0.2), transparent);
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 600;
            color: #d4af37;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-title {
                font-size: 42px;
                letter-spacing: 2px;
            }

            .teacher-showcase {
                flex-direction: column;
                gap: 30px;
                text-align: center;
            }

            .teacher-image {
                width: 250px;
                height: 320px;
            }

            .teacher-name {
                font-size: 36px;
            }

            .teacher-subject {
                font-size: 22px;
                letter-spacing: 2px;
            }

            .subtitle::before,
            .subtitle::after {
                width: 30px;
            }

            .subtitle::before {
                left: -50px;
            }

            .subtitle::after {
                right: -50px;
            }

            .brand-logo {
                top: 20px;
                right: 20px;
                font-size: 16px;
            }

            .section {
                padding: 30px 25px;
            }
        }

        @media (max-width: 480px) {
            .main-title {
                font-size: 36px;
            }

            .teacher-image {
                width: 220px;
                height: 280px;
            }

            .teacher-name {
                font-size: 32px;
            }

            .teacher-subject {
                font-size: 18px;
                letter-spacing: 1px;
            }

            .subtitle {
                letter-spacing: 4px;
            }
        }

    </style>
</head>
<body>
    <div class="hero-section">
        <div class="brand-logo">赶考状元</div>

        <div class="hero-content">
            <div class="title-decorations">
                <div class="geometric-shape shape-1"></div>
                <div class="geometric-shape shape-2"></div>
                <div class="shape-3"></div>
                <div class="geometric-shape shape-4"></div>
            </div>
            <div class="title-badge">高级启迪伴学师</div>
            <div class="subtitle">专业教育，成就未来</div>

            <div class="teacher-showcase">
                <div class="teacher-image-container">
                    <img src="teacher-avatar.jpg" alt="金膛教练" class="teacher-image">
                    <div class="decorative-circle"></div>
                </div>

                <div class="teacher-info">
                    <div class="teacher-name">金膛教练</div>
                    <div class="teacher-subject">伴学教龄：3年</div>
                </div>
            </div>

            <div class="golden-curve">
                <svg viewBox="0 0 400 120" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0,90 Q200,30 400,90" stroke="#d4af37" stroke-width="3" fill="none" opacity="0.8"/>
                </svg>
            </div>
        </div>
    </div>

    <div class="content-section">
        <div class="container">
            <!-- 1、教师简介 -->
            <div class="section">
                <div class="section-title">
                    <h2>1、教师简介</h2>
                </div>
                <div class="section-content">
                    <p><span class="highlight">经验：</span>3年线下冲刺班伴学经验，丰富先进带教理念，累计服务学员1000+</p>
                    <p><span class="highlight">平均提分：</span>熟系数学、物理、化学、生物学科学习方式引导</p>
                </div>
            </div>

            <!-- 2、教学风格 -->
            <div class="section">
                <div class="section-title">
                    <h2>2、教学风格</h2>
                </div>
                <div class="section-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px; margin-top: 20px;">
                        <div style="background: #f8f9ff; border-radius: 15px; padding: 30px; text-align: left; border-left: 4px solid #d4af37;">
                            <div style="font-size: 24px; color: #d4af37; margin-bottom: 15px;">�</div>
                            <div style="font-weight: 600; margin-bottom: 15px; color: #333; font-size: 18px;">认真负责</div>
                            <p style="color: #666; font-size: 15px; margin: 0; line-height: 1.6;">善于与家长沟通，各方面深入了解学员学习情况，精通规划学习计划</p>
                        </div>
                        <div style="background: #f8f9ff; border-radius: 15px; padding: 30px; text-align: left; border-left: 4px solid #d4af37;">
                            <div style="font-size: 24px; color: #d4af37; margin-bottom: 15px;">🧠</div>
                            <div style="font-weight: 600; margin-bottom: 15px; color: #333; font-size: 18px;">思路清晰</div>
                            <p style="color: #666; font-size: 15px; margin: 0; line-height: 1.6;">思维严谨，逻辑清晰，准确把控学科重难点易错点，善于归纳总结</p>
                        </div>
                    </div>

                    <div style="background: linear-gradient(135deg, #f8f9ff 0%, #e6ebff 100%); border-radius: 20px; padding: 35px; margin-top: 30px; text-align: center; border: 2px solid #d4af37; position: relative;">
                        <div style="font-size: 28px; color: #d4af37; margin-bottom: 20px;">💬</div>
                        <div style="font-weight: 700; margin-bottom: 20px; color: #d4af37; font-size: 20px;">老师寄语</div>
                        <p style="color: #333; font-size: 18px; margin: 0; line-height: 1.8; font-style: italic; font-weight: 500;">
                            "别做知识的搬运工，成为它的建筑师！我递砖块，你设计自己的思维大厦。"
                        </p>
                    </div>
                </div>
            </div>

            <!-- 3、学员评价 -->
            <div class="section">
                <div class="section-title">
                    <h2>3、学员评价</h2>
                </div>
                <div class="section-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px; margin-top: 10px;">
                        <!-- 评价截图1 -->
                        <div style="padding: 10px 0; border-left: 3px solid #d4af37; padding-left: 12px;">
                            <div style="margin-bottom: 6px;">
                                <div style="display: flex; align-items: center; margin-bottom: 6px;">
                                    <div style="width: 25px; height: 25px; background: #d4af37; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-right: 6px; font-size: 11px;">李</div>
                                    <div>
                                        <div style="font-weight: 600; color: #333; font-size: 12px;">李同学家长</div>
                                        <div style="font-size: 9px; color: #999;">今天 14:32</div>
                                    </div>
                                </div>
                                <div style="color: #333; font-size: 12px; line-height: 1.4;">
                                    金膛教练真的很负责！孩子数学从60分提升到85分，物理也有明显进步。教练会定期和我沟通孩子的学习情况，制定的学习计划很有针对性👍
                                </div>
                            </div>
                            <div style="color: #ffc107; font-size: 11px;">⭐⭐⭐⭐⭐</div>
                        </div>

                        <!-- 评价截图2 -->
                        <div style="padding: 10px 0; border-left: 3px solid #d4af37; padding-left: 12px;">
                            <div style="margin-bottom: 6px;">
                                <div style="display: flex; align-items: center; margin-bottom: 6px;">
                                    <div style="width: 25px; height: 25px; background: #d4af37; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-right: 6px; font-size: 11px;">王</div>
                                    <div>
                                        <div style="font-weight: 600; color: #333; font-size: 12px;">王同学</div>
                                        <div style="font-size: 9px; color: #999;">昨天 20:15</div>
                                    </div>
                                </div>
                                <div style="color: #333; font-size: 12px; line-height: 1.4;">
                                    跟着金膛教练学了2个月，化学成绩从70分提升到90分！教练思路很清晰，能把复杂的知识点讲得很简单，而且总结的方法特别实用🔥
                                </div>
                            </div>
                            <div style="color: #ffc107; font-size: 11px;">⭐⭐⭐⭐⭐</div>
                        </div>

                        <!-- 评价截图3 -->
                        <div style="padding: 10px 0; border-left: 3px solid #d4af37; padding-left: 12px;">
                            <div style="margin-bottom: 6px;">
                                <div style="display: flex; align-items: center; margin-bottom: 6px;">
                                    <div style="width: 25px; height: 25px; background: #d4af37; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-right: 6px; font-size: 11px;">陈</div>
                                    <div>
                                        <div style="font-weight: 600; color: #333; font-size: 12px;">陈同学家长</div>
                                        <div style="font-size: 9px; color: #999;">3天前 16:48</div>
                                    </div>
                                </div>
                                <div style="color: #333; font-size: 12px; line-height: 1.4;">
                                    感谢金膛教练！孩子生物从不及格到现在稳定80+，教练不仅教学方法好，还很关心孩子的心理状态。正如教练说的，真的成为了思维的建筑师！💪
                                </div>
                            </div>
                            <div style="color: #ffc107; font-size: 11px;">⭐⭐⭐⭐⭐</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>