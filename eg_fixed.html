<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优秀伴学师 - 金膛</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            padding: 15px;
            position: relative;
            height: 25vh;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .brand-logo {
            position: absolute;
            top: 8px;
            right: 15px;
            background: #d4af37;
            color: #1a1a1a;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            z-index: 10;
        }

        .hero-content {
            position: relative;
            z-index: 5;
            display: flex;
            align-items: center;
            gap: 12px;
            margin-top: 25px;
        }

        .teacher-image {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 2px solid #d4af37;
            object-fit: cover;
        }

        .teacher-info {
            flex: 1;
            position: relative;
        }

        .teacher-info::before {
            content: '';
            position: absolute;
            left: -10px;
            top: -5px;
            right: -10px;
            bottom: -5px;
            background: linear-gradient(45deg, #d4af37, #f4d03f, #d4af37);
            border-radius: 15px;
            opacity: 0.2;
            z-index: -1;
        }

        .teacher-name {
            font-size: 16px;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 2px;
        }

        .teacher-subject {
            font-size: 12px;
            color: #ccc;
        }

        .hero-title {
            text-align: center;
            margin: 8px 0;
            position: relative;
            z-index: 5;
        }

        .hero-title h1 {
            font-size: 18px;
            font-weight: bold;
            color: #d4af37;
            text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
            margin-bottom: 3px;
        }

        .hero-title .subtitle {
            font-size: 11px;
            color: #ccc;
            font-weight: normal;
        }

        /* Content Sections */
        .content {
            padding: 15px;
        }

        .section {
            margin-bottom: 15px;
        }

        .section-title {
            position: relative;
            margin-bottom: 8px;
        }

        .section-title h2 {
            font-size: 16px;
            color: #333;
            font-weight: 600;
            position: relative;
            padding-left: 15px;
            padding-right: 15px;
        }

        .section-title h2::before {
            content: "";
            position: absolute;
            left: -30px;
            top: 50%;
            width: 8px;
            height: 8px;
            background: #d4af37;
            border-radius: 50%;
            transform: translateY(-50%);
        }

        .section-title h2::after {
            content: "";
            position: absolute;
            right: -25px;
            top: 50%;
            width: 0;
            height: 0;
            border-left: 6px solid #d4af37;
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
            transform: translateY(-50%);
        }

        .section-title::before {
            content: "";
            position: absolute;
            left: -35px;
            top: 50%;
            width: 0;
            height: 0;
            border-right: 5px solid #d4af37;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
            transform: translateY(-50%);
        }

        .section-title::after {
            content: "";
            position: absolute;
            right: -35px;
            top: 50%;
            width: 6px;
            height: 6px;
            background: #d4af37;
            transform: translateY(-50%) rotate(45deg);
        }

        .section-content {
            color: #666;
            font-size: 13px;
            line-height: 1.5;
        }

        .section-content p {
            margin-bottom: 8px;
        }

        .highlight {
            color: #d4af37;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Hero Section -->
        <div class="hero-section">
            <div class="brand-logo">赶考状元</div>
            
            <div class="hero-content">
                <img src="teacher-avatar.jpg" alt="金膛老师" class="teacher-image">
                <div class="teacher-info">
                    <div class="teacher-name">金膛</div>
                    <div class="teacher-subject">数学 | 物理 | 化学 | 生物</div>
                </div>
            </div>
            
            <div class="hero-title">
                <h1>高级启迪伴学师</h1>
                <div class="subtitle">专业 · 负责 · 高效</div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- 教师简介 -->
            <div class="section">
                <div class="section-title">
                    <h2>教师简介</h2>
                </div>
                <div class="section-content">
                    <p><span class="highlight">经验：</span>3年线下冲刺班伴学经验，丰富先进带教理念，累计服务学员1000+</p>
                    <p><span class="highlight">平均提分：</span>熟系数学、物理、化学、生物学科学习方式引导</p>
                </div>
            </div>

            <!-- 教学风格 -->
            <div class="section">
                <div class="section-title">
                    <h2>教学风格</h2>
                </div>
                <div class="section-content">
                    <p><span class="highlight">认真负责：</span>善于与家长沟通，各方面深入了解学员学习情况，精通规划学习计划</p>
                    <p><span class="highlight">思路清晰：</span>思维严谨，逻辑清晰，准确把控学科重难点易错点，善于归纳总结</p>
                    <p><span class="highlight">老师寄语：</span>"别做知识的搬运工，成为它的建筑师！我递砖块，你设计自己的思维大厦。"</p>
                </div>
            </div>

            <!-- 学员评价 -->
            <div class="section">
                <div class="section-title">
                    <h2>学员评价</h2>
                </div>
                <div class="section-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-top: 8px;">
                        <!-- 学员评价截图1 -->
                        <div style="width: 100%; height: 120px; background: #f5f5f5; border: 2px dashed #d4af37; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #999; font-size: 14px;">
                            📸 学员评价截图1
                        </div>
                        
                        <!-- 学员评价截图2 -->
                        <div style="width: 100%; height: 120px; background: #f5f5f5; border: 2px dashed #d4af37; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #999; font-size: 14px;">
                            📸 学员评价截图2
                        </div>
                        
                        <!-- 学员评价截图3 -->
                        <div style="width: 100%; height: 120px; background: #f5f5f5; border: 2px dashed #d4af37; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #999; font-size: 14px;">
                            📸 学员评价截图3
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
