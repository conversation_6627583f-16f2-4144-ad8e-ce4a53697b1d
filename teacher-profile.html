<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>陈迎老师 - 数学专家</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
            min-height: 100vh;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background: #f8f9fa;
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        .header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            background: transparent;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: #333;
            z-index: 1000;
        }

        .header .back-btn {
            font-size: 18px;
            cursor: pointer;
        }

        .header .title {
            font-size: 16px;
            font-weight: 500;
        }

        .header .more-btn {
            font-size: 18px;
            cursor: pointer;
        }

        .hero-section {
            position: relative;
            background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 60px 20px 30px;
            margin-top: 0;
            overflow: hidden;
            height: 160px;
            display: flex;
            align-items: center;
        }

        /* 质量标签 - 左上角 */
        .quality-badge {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #333;
            color: white;
            padding: 8px 12px;
            font-size: 11px;
            font-weight: 700;
            letter-spacing: 1px;
            z-index: 3;
        }

        /* 老师头像 - 左侧圆形头像 */
        .teacher-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            overflow: hidden;
            flex-shrink: 0;
            border: 3px solid #007bff;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
        }

        .teacher-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 右侧信息区域 */
        .teacher-info-right {
            flex: 1;
            margin-left: 20px;
            z-index: 2;
        }

        /* 装饰性几何元素 */
        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 1;
        }

        .decorative-elements::before {
            content: '';
            position: absolute;
            top: 80px;
            left: 50px;
            width: 60px;
            height: 60px;
            border: 2px solid #007bff;
            border-radius: 50%;
            transform: rotate(-15deg);
        }

        .decorative-elements::after {
            content: '';
            position: absolute;
            bottom: 100px;
            left: 180px;
            width: 40px;
            height: 40px;
            background: #ffc107;
            transform: rotate(45deg);
        }

        /* 蓝色装饰点 */
        .blue-dots {
            position: absolute;
            top: 120px;
            left: 160px;
            z-index: 1;
        }

        .blue-dots::before {
            content: '✦';
            color: #007bff;
            font-size: 20px;
            position: absolute;
        }

        .blue-dots::after {
            content: '✦';
            color: #007bff;
            font-size: 14px;
            position: absolute;
            top: 30px;
            left: 25px;
        }



        /* 老师姓名 */
        .teacher-name {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
        }

        /* 科目标题 */
        .teacher-subject {
            font-size: 16px;
            color: #666;
            font-weight: 500;
            margin-bottom: 10px;
        }

        /* 质量标签 - 小标签 */
        .quality-badge-small {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .content-section {
            padding: 30px 20px;
            background: white;
            margin-top: 0;
            position: relative;
            z-index: 2;
            border-radius: 20px 20px 0 0;
        }

        .section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 16px;
            position: relative;
            padding-left: 20px;
        }

        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .intro-content {
            background: white;
            padding: 0;
        }

        .intro-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            font-size: 14px;
            line-height: 1.6;
            color: #666;
        }

        .intro-item:last-child {
            margin-bottom: 0;
        }

        .intro-item::before {
            content: '';
            width: 6px;
            height: 6px;
            background: #ffc107;
            border-radius: 50%;
            margin-right: 10px;
            margin-top: 8px;
            flex-shrink: 0;
        }

        .style-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .style-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid #007bff;
            margin-bottom: 12px;
        }

        .style-card-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
        }

        .style-card-desc {
            font-size: 13px;
            color: #666;
            line-height: 1.5;
            margin: 0;
        }

        .feedback-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .feedback-card {
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
        }

        .feedback-image {
            width: 100%;
            height: 200px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 14px;
        }

        .feedback-content {
            padding: 16px;
        }

        .feedback-rating {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .stars {
            color: #ffc107;
            margin-right: 8px;
        }

        .rating-text {
            font-size: 12px;
            color: #6c757d;
        }

        .feedback-text {
            font-size: 14px;
            color: #495057;
            line-height: 1.6;
        }

        .footer {
            text-align: center;
            padding: 40px 20px;
            background: #f8f9fa;
            margin-top: 40px;
        }

        .contact-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 16px 32px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }

        .contact-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="back-btn">×</div>
            <div class="title">个性化 | 数学·陈迎老师</div>
            <div class="more-btn">⋯</div>
        </div>

        <!-- Hero Section -->
        <div class="hero-section">
            <!-- 质量标签 -->
            <div class="quality-badge">QUALITY<br>CLASS</div>

            <!-- 装饰元素 -->
            <div class="decorative-elements"></div>
            <div class="blue-dots"></div>

            <!-- 老师头像 - 左侧圆形头像 -->
            <div class="teacher-avatar">
                <img src="https://images.unsplash.com/photo-1494790108755-2616c9c0e8e5?w=200&h=200&fit=crop&crop=face" alt="陈迎老师">
            </div>

            <!-- 右侧信息区域 -->
            <div class="teacher-info-right">
                <div class="teacher-name">陈迎</div>
                <div class="teacher-subject">高中数学专家导师</div>
                <div class="quality-badge-small">金牌导师</div>
            </div>
        </div>

        <!-- Content Sections -->
        <div class="content-section">
            <!-- 教师简介 -->
            <div class="section">
                <h2 class="section-title">教师简介</h2>
                <div class="intro-content">
                    <div class="intro-item">6年以上中学数学教学经验，创新备考生辅导教育机构经验，曾任数学学科组组长，累计授课学生达3000+</div>
                    <div class="intro-item">善于根据学生基础制定针对性教学方案，助力学生成绩提升720-40分</div>
                    <div class="intro-item">精通各种学习方法，高中数学重难点，深谙考试重点难点</div>
                </div>
            </div>

            <!-- 伴学经历 -->
            <div class="section">
                <h2 class="section-title">伴学经历</h2>
                <div class="intro-content">
                    <div class="intro-item">全天候在线答疑，及时解决学生学习疑问</div>
                    <div class="intro-item">每周定期学习进度跟踪，确保学习效果</div>
                    <div class="intro-item">个性化学习计划制定，因材施教</div>
                    <div class="intro-item">课后作业批改与详细反馈</div>
                </div>
            </div>

            <!-- 教学风格 -->
            <div class="section">
                <h2 class="section-title">教学风格</h2>
                <div class="style-grid">
                    <div class="style-card">
                        <div class="style-card-title">逻辑清晰</div>
                        <div class="style-card-desc">课堂结构严谨，逻辑清晰，数学于学生思维基础能力。</div>
                    </div>
                    <div class="style-card">
                        <div class="style-card-title">全方位培养学生能力</div>
                        <div class="style-card-desc">从基础上提升学生数学能力，全方位培养解决问题的能力。</div>
                    </div>
                    <div class="style-card">
                        <div class="style-card-title">数学素养</div>
                        <div class="style-card-desc">课程的主动权教给孩子，老师明了学习的"输入"与"输出"。</div>
                    </div>
                </div>
            </div>

            <!-- 好评反馈 -->
            <div class="section">
                <h2 class="section-title">好评反馈</h2>
                <div class="feedback-grid">
                    <div class="feedback-card">
                        <div class="feedback-image">
                            点击上传学生反馈截图
                        </div>
                        <div class="feedback-content">
                            <div class="feedback-rating">
                                <div class="stars">★★★★★</div>
                                <div class="rating-text">5.0分</div>
                            </div>
                            <div class="feedback-text">陈老师讲课非常清晰，能够把复杂的数学问题讲得很简单易懂，我的数学成绩提升了很多！</div>
                        </div>
                    </div>
                    <div class="feedback-card">
                        <div class="feedback-image">
                            点击上传学生反馈截图
                        </div>
                        <div class="feedback-content">
                            <div class="feedback-rating">
                                <div class="stars">★★★★★</div>
                                <div class="rating-text">5.0分</div>
                            </div>
                            <div class="feedback-text">老师很有耐心，会根据我的学习情况制定专门的学习计划，现在做题思路更清晰了。</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <button class="contact-btn">立即预约试听课</button>
        </div>
    </div>

    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 添加滚动动画效果
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // 为所有section添加动画
            document.querySelectorAll('.section').forEach(section => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(30px)';
                section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(section);
            });

            // 反馈图片上传模拟
            document.querySelectorAll('.feedback-image').forEach(img => {
                img.addEventListener('click', function() {
                    alert('这里可以集成图片上传功能');
                });
            });

            // 预约按钮点击事件
            document.querySelector('.contact-btn').addEventListener('click', function() {
                alert('跳转到预约页面或打开预约弹窗');
            });
        });
    </script>
</body>
</html>
