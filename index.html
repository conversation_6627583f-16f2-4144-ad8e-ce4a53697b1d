<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>名师简介 | 赶考状元</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        html {
            -webkit-user-select: none;
            user-select: none;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        :root {
            --primary: #1e3a8a;
            --secondary: #3b82f6;
            --accent: #0ea5e9;
            --light: #f8fafc;
            --gray: #64748b;
            --card-bg: rgba(255, 255, 255, 0.98);
            --shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            --gradient-bg: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 50%, #fafbff 100%);
        }
        
        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #334155;
            line-height: 1.6;
            min-height: 100vh;
            padding: 20px;
            font-weight: 400;
        }

        .container {
            max-width: 420px;
            margin: 0 auto;
            background: #ffffff;
            min-height: calc(100vh - 40px);
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(226, 232, 240, 0.8);
        }
        
        /* 头部区域 - 简历风格 */
        .header-section {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            padding: 0;
            position: relative;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="hexagons" width="60" height="60" patternUnits="userSpaceOnUse"><polygon points="30,2 52,17 52,43 30,58 8,43 8,17" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23hexagons)"/></svg>');
        }

        .logo {
            color: white;
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 6px;
            position: relative;
            z-index: 2;
            letter-spacing: 2px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .logo span {
            color: #38bdf8;
            font-weight: 300;
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 11px;
            letter-spacing: 4px;
            position: relative;
            z-index: 2;
            font-weight: 300;
            text-transform: uppercase;
        }
        
        /* 教师卡片 - 简历风格 */
        .teacher-card {
            background: white;
            margin: -60px 30px 30px;
            border-radius: 0;
            box-shadow: none;
            overflow: visible;
            position: relative;
            z-index: 3;
            border: none;
        }

        .teacher-header {
            padding: 50px 30px 40px;
            text-align: center;
            background: white;
            position: relative;
            border-bottom: 2px solid #f1f5f9;
        }
        
        .teacher-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid #f8fafc;
            object-fit: cover;
            margin: 0 auto 20px;
            display: block;
            box-shadow: 0 8px 32px rgba(30, 41, 59, 0.15);
            position: relative;
        }
        
        .teacher-name {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 6px;
            color: #1e293b;
            letter-spacing: 1px;
        }

        .teacher-subject {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 25px;
            font-weight: 500;
            position: relative;
        }

        .teacher-subject::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            background: linear-gradient(90deg, var(--secondary), var(--accent));
        }
        
        .teacher-stats {
            display: flex;
            justify-content: space-around;
            padding: 0 30px;
            margin-top: 10px;
        }

        .stat-item {
            text-align: center;
            position: relative;
        }

        .stat-item:not(:last-child)::after {
            content: '';
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            width: 1px;
            height: 30px;
            background: #e2e8f0;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 800;
            color: var(--primary);
            display: block;
            line-height: 1;
        }

        .stat-label {
            font-size: 11px;
            color: var(--gray);
            margin-top: 6px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        /* 内容区域 - 简历风格 */
        .content-section {
            padding: 0 30px 30px;
        }

        .section {
            background: white;
            border-radius: 0;
            padding: 30px 0;
            margin-bottom: 0;
            box-shadow: none;
            border: none;
            border-bottom: 1px solid #f1f5f9;
            position: relative;
        }

        .section:last-child {
            border-bottom: none;
        }
        
        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            color: #1e293b;
            font-size: 20px;
            font-weight: 700;
            position: relative;
            padding-left: 20px;
        }

        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 24px;
            background: linear-gradient(135deg, var(--secondary), var(--accent));
            border-radius: 2px;
        }

        .section-title i {
            margin-right: 12px;
            color: var(--secondary);
            font-size: 18px;
            width: 20px;
            text-align: center;
        }
        
        .section-content {
            color: #475569;
            line-height: 1.8;
            font-size: 15px;
        }
        
        /* 教师简介样式 - 简历风格 */
        .intro-item {
            margin-bottom: 20px;
            font-size: 15px;
            line-height: 1.7;
            display: flex;
            align-items: flex-start;
            position: relative;
            padding-left: 50px;
        }

        .intro-number {
            position: absolute;
            left: 0;
            top: 2px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            border-radius: 8px;
            font-weight: 700;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.25);
        }
        
        /* 教学风格卡片 - 简历风格 */
        .teaching-styles {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .style-card {
            background: #f8fafc;
            border-radius: 12px;
            padding: 24px 16px;
            text-align: center;
            border: 2px solid #f1f5f9;
            transition: all 0.3s ease;
            position: relative;
        }

        .style-card:hover {
            border-color: var(--secondary);
            transform: translateY(-3px);
            box-shadow: 0 12px 32px rgba(30, 58, 138, 0.15);
        }
        
        .style-card i {
            font-size: 28px;
            color: var(--secondary);
            margin-bottom: 12px;
            display: block;
        }

        .style-title {
            font-weight: 700;
            margin-bottom: 8px;
            color: var(--primary);
            font-size: 15px;
        }

        .style-card p {
            font-size: 12px;
            color: var(--gray);
            line-height: 1.5;
            margin: 0;
        }
        
        /* 评价区域 - 简历风格 */
        .reviews-container {
            display: grid;
            gap: 20px;
            margin-top: 20px;
        }

        .review-item {
            background: #f8fafc;
            border-radius: 12px;
            padding: 24px;
            border: 2px solid #f1f5f9;
            position: relative;
        }

        .review-item::before {
            content: '"';
            position: absolute;
            top: -10px;
            left: 20px;
            font-size: 40px;
            color: var(--secondary);
            font-family: serif;
            line-height: 1;
        }
        
        .review-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .review-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            margin-right: 10px;
            object-fit: cover;
        }
        
        .review-info {
            flex: 1;
        }
        
        .review-name {
            font-weight: 600;
            font-size: 14px;
            color: var(--primary);
        }
        
        .review-date {
            font-size: 12px;
            color: var(--gray);
        }
        
        .review-stars {
            color: #0ea5e9;
            margin: 10px 0;
            font-size: 16px;
        }
        
        .review-content {
            font-size: 14px;
            color: #555;
            font-style: italic;
            line-height: 1.5;
        }
        
        /* 截图网格 */
        .screenshot-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 16px;
        }
        
        .screenshot {
            aspect-ratio: 3/4;
            background: #f0f0f0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray);
            font-size: 12px;
            border: 1px solid #e0e0e0;
        }
        
        /* 页脚 */
        .footer {
            text-align: center;
            padding: 30px 20px;
            color: var(--gray);
            font-size: 13px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-top: 1px solid #e2e8f0;
        }
        
        /* 响应式调整 */
        @media (max-width: 375px) {
            .container {
                max-width: 100%;
            }
            
            .teacher-card {
                margin: -30px 15px 15px;
            }
            
            .content-section {
                padding: 0 15px 15px;
            }
            
            .section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <div class="header-section">
            <div class="logo">赶考<span>状元</span></div>
            <div class="subtitle">LEADING EDUCATION GROUP</div>
        </div>
        
        <!-- 教师卡片 -->
        <div class="teacher-card">
            <div class="teacher-header">
                <img src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80" alt="张明华老师" class="teacher-avatar">
                <h1 class="teacher-name">张明华 老师</h1>
                <div class="teacher-subject">高中数学 | 教研组长</div>
                <div class="teacher-stats">
                    <div class="stat-item">
                        <span class="stat-value">12年</span>
                        <div class="stat-label">教龄</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">98%</span>
                        <div class="stat-label">提分率</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">1500+</span>
                        <div class="stat-label">学生</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-section">
            <!-- 教师简介 -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-user-graduate"></i>
                    <h2>教师简介</h2>
                </div>
                <div class="section-content">
                    <div class="intro-item">
                        <span class="intro-number">1</span>
                        <span>6年以上中学数学教学经验，创新备考生辅导教育机构经验，曾任数学学科组组长，累计授课学生达3000+</span>
                    </div>
                    <div class="intro-item">
                        <span class="intro-number">2</span>
                        <span>善于根据学生基础制定针对性教学方案，助力学生成绩提升720-40分</span>
                    </div>
                    <div class="intro-item">
                        <span class="intro-number">3</span>
                        <span>精通各种学习方法，高中数学重难点，深谙考试重点难点</span>
                    </div>
                </div>
            </div>

            <!-- 伴学经历 -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-clock"></i>
                    <h2>伴学经历</h2>
                </div>
                <div class="section-content">
                    <p>张老师每周可提供<strong>20小时</strong>的个性化辅导时间，包括：</p>
                    <ul style="padding-left: 20px; margin: 15px 0;">
                        <li>每周一、三、五晚 19:00-21:00（小组课）</li>
                        <li>每周二、四晚 19:00-21:00（1对1辅导）</li>
                        <li>周六全天 9:00-17:00（专题强化班）</li>
                    </ul>
                    <p>张老师坚持"精耕细作"的教学理念，每学期仅接收<strong>30名</strong>学生，确保每位学生都能获得充分的关注和个性化指导。他承诺24小时内解答学生疑问，每周提供学习反馈报告。</p>
                </div>
            </div>

            <!-- 伴学风格 -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-chalkboard-teacher"></i>
                    <h2>伴学风格</h2>
                </div>
                <div class="teaching-styles">
                    <div class="style-card">
                        <i class="fas fa-lightbulb"></i>
                        <div class="style-title">启发式教学</div>
                        <p>通过问题引导思考，激发学生自主探究能力</p>
                    </div>
                    <div class="style-card">
                        <i class="fas fa-puzzle-piece"></i>
                        <div class="style-title">体系化构建</div>
                        <p>帮助建立完整的知识框架，融会贯通</p>
                    </div>
                    <div class="style-card">
                        <i class="fas fa-heart"></i>
                        <div class="style-title">激励型互动</div>
                        <p>善于发现学生闪光点，增强学习信心</p>
                    </div>
                    <div class="style-card">
                        <i class="fas fa-laugh-beam"></i>
                        <div class="style-title">轻松氛围</div>
                        <p>幽默风趣的教学语言，化解学习压力</p>
                    </div>
                </div>
            </div>

            <!-- 学员评价 -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-star"></i>
                    <h2>学员评价</h2>
                </div>

                <div class="reviews-container">
                    <div class="review-item">
                        <div class="review-header">
                            <img src="https://randomuser.me/api/portraits/women/45.jpg" class="review-avatar">
                            <div class="review-info">
                                <div class="review-name">李同学</div>
                                <div class="review-date">2023-06-15</div>
                            </div>
                        </div>
                        <div class="review-stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="review-content">
                            "张老师的函数专题课让我突破了瓶颈，原来觉得很难的知识点，经过他的讲解变得特别清晰易懂。"
                        </div>
                    </div>

                    <div class="review-item">
                        <div class="review-header">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" class="review-avatar">
                            <div class="review-info">
                                <div class="review-name">王同学</div>
                                <div class="review-date">2023-05-22</div>
                            </div>
                        </div>
                        <div class="review-stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="review-content">
                            "三个月时间数学从90分提升到135分！张老师的解题思路太神奇了，每次都能直击要害。"
                        </div>
                    </div>
                </div>

                <h3 style="margin: 20px 0 12px; color: var(--primary); font-size: 16px;">学员反馈截图</h3>
                <div class="screenshot-grid">
                    <div class="screenshot">反馈截图1</div>
                    <div class="screenshot">反馈截图2</div>
                    <div class="screenshot">反馈截图3</div>
                    <div class="screenshot">反馈截图4</div>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <p>&copy; 2023 赶考状元教育集团 | 专业教育，成就未来</p>
        </div>
    </div>
</body>
</html>
