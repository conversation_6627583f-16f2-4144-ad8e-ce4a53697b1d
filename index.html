<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>名师简介 | 赶考状元</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        html {
            -webkit-user-select: none;
            user-select: none;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        :root {
            --primary: #1e3a8a;
            --secondary: #3b82f6;
            --accent: #0ea5e9;
            --light: #f8fafc;
            --gray: #64748b;
            --card-bg: rgba(255, 255, 255, 0.98);
            --shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            --gradient-bg: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 50%, #fafbff 100%);
        }
        
        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #334155;
            line-height: 1.6;
            min-height: 100vh;
            padding: 20px;
            font-weight: 400;
        }

        .container {
            max-width: 420px;
            margin: 0 auto;
            background: #ffffff;
            min-height: calc(100vh - 40px);
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(226, 232, 240, 0.8);
        }
        
        /* 头部区域 - 简历风格 */
        .header-section {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            padding: 0;
            position: relative;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="hexagons" width="60" height="60" patternUnits="userSpaceOnUse"><polygon points="30,2 52,17 52,43 30,58 8,43 8,17" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23hexagons)"/></svg>');
        }

        .logo {
            color: white;
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 6px;
            position: relative;
            z-index: 2;
            letter-spacing: 2px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .logo span {
            color: #38bdf8;
            font-weight: 300;
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 11px;
            letter-spacing: 4px;
            position: relative;
            z-index: 2;
            font-weight: 300;
            text-transform: uppercase;
        }
        
        /* 教师卡片 - 简历风格 */
        .teacher-card {
            background: white;
            margin: -60px 30px 30px;
            border-radius: 0;
            box-shadow: none;
            overflow: visible;
            position: relative;
            z-index: 3;
            border: none;
        }

        .teacher-header {
            padding: 50px 30px 40px;
            background: white;
            position: relative;
            border-bottom: 2px solid #f1f5f9;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .teacher-avatar {
            width: 130px;
            height: 160px;
            border-radius: 8px;
            border: 3px solid #f8fafc;
            object-fit: cover;
            box-shadow: 0 8px 32px rgba(30, 41, 59, 0.15);
            position: relative;
            flex-shrink: 0;
        }

        .teacher-info {
            flex: 1;
            text-align: left;
            padding-right: 20px;
        }
        
        .teacher-name {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 6px;
            color: #1e293b;
            letter-spacing: 1px;
        }

        .teacher-subject {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 25px;
            font-weight: 500;
            position: relative;
        }

        .teacher-subject::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            background: linear-gradient(90deg, var(--secondary), var(--accent));
        }
        
        .teacher-stats {
            display: flex;
            justify-content: center;
            padding: 0;
            margin-top: 10px;
        }

        .stat-item {
            text-align: center;
            position: relative;
        }

        .stat-item:not(:last-child)::after {
            content: '';
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            width: 1px;
            height: 30px;
            background: #e2e8f0;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 800;
            color: var(--primary);
            display: block;
            line-height: 1;
        }

        .stat-label {
            font-size: 11px;
            color: var(--gray);
            margin-top: 6px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        /* 内容区域 - 简历风格 */
        .content-section {
            padding: 0 30px 30px;
        }

        .section {
            background: white;
            border-radius: 0;
            padding: 30px 0;
            margin-bottom: 0;
            box-shadow: none;
            border: none;
            border-bottom: 1px solid #f1f5f9;
            position: relative;
        }

        .section:last-child {
            border-bottom: none;
        }
        
        .section-title {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            color: #1e293b;
            font-size: 20px;
            font-weight: 700;
            position: relative;
            padding-left: 20px;
        }

        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 24px;
            background: linear-gradient(135deg, var(--secondary), var(--accent));
            border-radius: 2px;
        }

        .section-title i {
            margin-right: 12px;
            color: var(--secondary);
            font-size: 18px;
            width: 20px;
            text-align: center;
        }
        
        .section-content {
            color: #475569;
            line-height: 1.8;
            font-size: 15px;
        }
        
        /* 教师简介样式 - 简历风格 */
        .intro-item {
            margin-bottom: 20px;
            font-size: 15px;
            line-height: 1.7;
            display: flex;
            align-items: flex-start;
            position: relative;
            padding-left: 50px;
        }

        .intro-number {
            position: absolute;
            left: 0;
            top: 2px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            border-radius: 8px;
            font-weight: 700;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.25);
        }
        
        /* 教学风格卡片 - 简历风格 */
        .teaching-styles {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .style-card {
            background: #f8fafc;
            border-radius: 12px;
            padding: 24px 16px;
            text-align: center;
            border: 2px solid #f1f5f9;
            transition: all 0.3s ease;
            position: relative;
        }

        .style-card:hover {
            border-color: var(--secondary);
            transform: translateY(-3px);
            box-shadow: 0 12px 32px rgba(30, 58, 138, 0.15);
        }
        
        .style-card i {
            font-size: 28px;
            color: var(--secondary);
            margin-bottom: 12px;
            display: block;
        }

        .style-title {
            font-weight: 700;
            margin-bottom: 8px;
            color: var(--primary);
            font-size: 15px;
        }

        .style-card p {
            font-size: 12px;
            color: var(--gray);
            line-height: 1.5;
            margin: 0;
        }
        
        /* 评价区域 - 简历风格 */
        .reviews-container {
            display: grid;
            gap: 20px;
            margin-top: 20px;
        }

        .review-item {
            background: #f8fafc;
            border-radius: 12px;
            padding: 24px;
            border: 2px solid #f1f5f9;
            position: relative;
        }

        .review-item::before {
            content: '"';
            position: absolute;
            top: -10px;
            left: 20px;
            font-size: 40px;
            color: var(--secondary);
            font-family: serif;
            line-height: 1;
        }
        
        .review-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .review-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            margin-right: 10px;
            object-fit: cover;
        }
        
        .review-info {
            flex: 1;
        }
        
        .review-name {
            font-weight: 600;
            font-size: 14px;
            color: var(--primary);
        }
        
        .review-date {
            font-size: 12px;
            color: var(--gray);
        }
        
        .review-stars {
            color: #0ea5e9;
            margin: 10px 0;
            font-size: 16px;
        }
        
        .review-content {
            font-size: 14px;
            color: #555;
            font-style: italic;
            line-height: 1.5;
        }
        
        /* 截图网格 */
        .screenshot-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 16px;
        }
        
        .screenshot {
            aspect-ratio: 3/4;
            background: #f0f0f0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray);
            font-size: 12px;
            border: 1px solid #e0e0e0;
        }
        
        /* 页脚 */
        .footer {
            text-align: center;
            padding: 30px 20px;
            color: var(--gray);
            font-size: 13px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-top: 1px solid #e2e8f0;
        }
        
        /* 响应式调整 */
        @media (max-width: 375px) {
            .container {
                max-width: 100%;
            }
            
            .teacher-card {
                margin: -30px 15px 15px;
            }
            
            .content-section {
                padding: 0 15px 15px;
            }
            
            .section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <div class="header-section">
            <div class="logo">赶考<span>状元</span></div>
            <div class="subtitle">LEADING EDUCATION GROUP</div>
        </div>
        
        <!-- 教师卡片 -->
        <div class="teacher-card">
            <div class="teacher-header">
                <div class="teacher-info">
                    <h1 class="teacher-name">张芳 教练</h1>
                    <div class="teacher-subject">高级启迪伴学师</div>
                    <div class="teacher-stats">
                        <div class="stat-item">
                            <span class="stat-value">5年</span>
                            <div class="stat-label">伴学教龄</div>
                        </div>
                    </div>
                </div>
                <img src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=400&q=80" alt="张芳教练" class="teacher-avatar">
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-section">
            <!-- 教练简介 -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-user-graduate"></i>
                    <h2>教练简介</h2>
                </div>
                <div class="section-content">
                    <div class="intro-item">
                        <span class="intro-number">1</span>
                        <span><strong>经验：</strong>线下伴学带教5年经验，多年头部教育机构带教经验，累计带教学生量2000+</span>
                    </div>
                    <div class="intro-item">
                        <span class="intro-number">2</span>
                        <span><strong>平均提分：</strong>擅长小初段学龄学员伴学，亲切热情，擅长和孩子建立关系，引导孩子成绩提高20到50分</span>
                    </div>
                </div>
            </div>



            <!-- 教学风格 -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-chalkboard-teacher"></i>
                    <h2>教学风格</h2>
                </div>
                <div class="teaching-styles">
                    <div class="style-card">
                        <i class="fas fa-theater-masks"></i>
                        <div class="style-title">声情并茂</div>
                        <p>高互动模式学习，上张老师的课不枯燥，孩子愿意上</p>
                    </div>
                    <div class="style-card">
                        <i class="fas fa-search"></i>
                        <div class="style-title">善于挖掘根本问题</div>
                        <p>与学员建立友好教学关系，加强认同感，善于挖掘学员认同感</p>
                    </div>
                </div>
            </div>

            <!-- 教师寄语 -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-quote-left"></i>
                    <h2>教师寄语</h2>
                </div>
                <div class="section-content">
                    <div class="quote-container" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); padding: 20px; border-radius: 15px; border-left: 4px solid var(--primary); margin: 15px 0; position: relative;">
                        <div style="font-size: 16px; line-height: 1.8; color: #334155; font-style: italic; text-align: center;">
                            "教室是你们的工坊，作品打上你的烙印。我打磨工具，你创造独一无二的价值！"
                        </div>
                        <div style="text-align: right; margin-top: 15px; color: var(--primary); font-weight: 600;">
                            —— 张芳教练
                        </div>
                    </div>
                </div>
            </div>

            <!-- 学员评价 -->
            <div class="section">
                <div class="section-title">
                    <i class="fas fa-star"></i>
                    <h2>学员评价</h2>
                </div>

                <div class="reviews-container">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px;">
                        <!-- 学员评价图片占位1 -->
                        <div style="width: 100%; height: 150px; background: #f8fafc; border: 2px dashed var(--primary); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: var(--gray); font-size: 14px; flex-direction: column;">
                            <i class="fas fa-image" style="font-size: 24px; margin-bottom: 8px; color: var(--primary);"></i>
                            学员评价截图1
                        </div>

                        <!-- 学员评价图片占位2 -->
                        <div style="width: 100%; height: 150px; background: #f8fafc; border: 2px dashed var(--primary); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: var(--gray); font-size: 14px; flex-direction: column;">
                            <i class="fas fa-image" style="font-size: 24px; margin-bottom: 8px; color: var(--primary);"></i>
                            学员评价截图2
                        </div>

                        <!-- 学员评价图片占位3 -->
                        <div style="width: 100%; height: 150px; background: #f8fafc; border: 2px dashed var(--primary); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: var(--gray); font-size: 14px; flex-direction: column;">
                            <i class="fas fa-image" style="font-size: 24px; margin-bottom: 8px; color: var(--primary);"></i>
                            学员评价截图3
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <p>专业教育，成就未来</p>
        </div>
    </div>
</body>
</html>
